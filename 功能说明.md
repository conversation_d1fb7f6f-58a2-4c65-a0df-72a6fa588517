# TM小助手 - 新增接口监听功能

## 🆕 新增功能

### 📋 接口监听悬浮球
在原有的清理弹窗悬浮球基础上，新增了一个接口监听悬浮球：

- **位置**：页面右侧50%高度处
- **图标**：📋 (剪贴板图标)
- **颜色**：粉色渐变 (未监听状态) / 蓝色渐变 (监听状态)
- **功能**：点击开始/停止监听 `querySubOrderList` 接口

## 🎯 功能特性

### 1. 智能接口拦截
- **Fetch API 拦截**：自动拦截所有 fetch 请求
- **XMLHttpRequest 拦截**：兼容传统的 XHR 请求
- **目标接口识别**：只处理包含 `querySubOrderList` 的请求

### 2. 响应数据处理
- **JSON 格式化**：自动解析并格式化 JSON 响应
- **时间戳记录**：记录接口调用的具体时间
- **URL 记录**：保存完整的请求 URL
- **数据结构化**：按照统一格式组织数据

### 3. 剪切板功能
- **现代 API 优先**：优先使用 `navigator.clipboard` API
- **兼容性备用**：自动降级到 `document.execCommand` 方法
- **错误处理**：完善的错误处理和用户提示

### 4. 用户体验优化
- **状态指示**：监听状态下悬浮球变蓝色并有脉冲动画
- **实时通知**：操作成功/失败的即时通知提示
- **拖拽支持**：两个悬浮球都支持拖拽移动
- **自动清理**：页面卸载时自动停止监听

## 📊 复制的数据格式

```json
{
  "timestamp": "2024-01-01 12:00:00",
  "url": "https://example.com/api/querySubOrderList",
  "response": {
    "code": 0,
    "message": "success",
    "data": {
      // 实际的响应数据
    }
  }
}
```

## 🎮 使用方法

### 开始监听
1. 点击 📋 悬浮球
2. 悬浮球变为蓝色并显示"监听中..."
3. 出现成功通知提示

### 停止监听
1. 再次点击 📋 悬浮球
2. 悬浮球恢复粉色并显示"监听接口"
3. 出现停止监听的通知提示

### 数据复制
- 监听状态下，当检测到 `querySubOrderList` 接口请求时
- 自动将响应数据复制到剪切板
- 显示复制成功的通知提示

## 🔧 技术实现

### 接口拦截机制
```javascript
// Fetch API 拦截
window.fetch = function(...args) {
    return originalFetch.apply(this, args).then(response => {
        if (url.includes('querySubOrderList')) {
            // 处理响应数据
        }
        return response;
    });
};

// XMLHttpRequest 拦截
XMLHttpRequest.prototype.send = function(...args) {
    if (this._url.includes('querySubOrderList')) {
        this.addEventListener('load', function() {
            // 处理响应数据
        });
    }
    return originalXHRSend.apply(this, args);
};
```

### 剪切板操作
```javascript
// 现代浏览器
navigator.clipboard.writeText(text).then(() => {
    showNotification('复制成功', 'success');
});

// 兼容性备用
document.execCommand('copy');
```

## 🎨 样式设计

### 悬浮球样式
- **清理弹窗球**：紫色渐变 (`#667eea` → `#764ba2`)
- **监听接口球**：粉色渐变 (`#f093fb` → `#f5576c`)
- **监听状态球**：蓝色渐变 (`#4facfe` → `#00f2fe`)

### 动画效果
- **脉冲动画**：监听状态下的呼吸灯效果
- **悬停放大**：鼠标悬停时的缩放效果
- **点击反馈**：点击时的缩小效果

## 🛡️ 安全考虑

### 数据处理
- 只处理目标接口的响应
- 不修改原始请求和响应
- 安全的 JSON 解析和错误处理

### 权限要求
- 无需额外的浏览器权限
- 使用标准的 Web API
- 不涉及跨域请求

## 🧪 测试方法

1. 打开 `测试页面.html`
2. 观察两个悬浮球是否正常显示
3. 测试弹窗清理功能
4. 测试接口监听功能
5. 检查剪切板内容

## 🔄 兼容性

- **现代浏览器**：完整功能支持
- **旧版浏览器**：自动降级到兼容方法
- **移动端**：响应式设计，支持触摸操作

## 📝 更新日志

### v1.0.6 (新增功能)
- ✅ 新增接口监听悬浮球
- ✅ 实现 querySubOrderList 接口监听
- ✅ 添加剪切板复制功能
- ✅ 优化用户体验和通知系统
- ✅ 完善错误处理机制
