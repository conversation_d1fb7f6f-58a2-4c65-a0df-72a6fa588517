// TM小助手 - 悬浮球功能
(function() {
    'use strict';

    // 全局状态
    let isListening = false;
    let originalFetch = null;
    let originalXHROpen = null;
    let originalXHRSend = null;

    // 创建清理弹窗悬浮球
    function createFloatBall() {
        // 检查是否已存在悬浮球
        if (document.getElementById('tm-float-ball')) {
            return;
        }

        // 创建悬浮球容器
        const floatBall = document.createElement('div');
        floatBall.id = 'tm-float-ball';
        floatBall.innerHTML = `
            <div class="tm-float-ball-container">
                <div class="tm-float-ball-icon">🧹</div>
                <div class="tm-float-ball-text">清理弹窗</div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(floatBall);

        // 添加拖拽功能
        makeDraggable(floatBall);

        // 添加点击事件 - 直接清理弹窗
        floatBall.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            clearPopups();
        });
    }

    // 创建监听接口悬浮球
    function createListenerBall() {
        // 检查是否已存在监听球
        if (document.getElementById('tm-listener-ball')) {
            return;
        }

        // 创建监听球容器
        const listenerBall = document.createElement('div');
        listenerBall.id = 'tm-listener-ball';
        listenerBall.innerHTML = `
            <div class="tm-float-ball-container">
                <div class="tm-float-ball-icon">📋</div>
                <div class="tm-float-ball-text">监听接口</div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(listenerBall);

        // 添加拖拽功能
        makeDraggable(listenerBall);

        // 添加点击事件 - 切换监听状态
        listenerBall.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleListener();
        });
    }

    // 添加样式
    function addStyles() {
        if (document.getElementById('tm-float-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'tm-float-styles';
        style.textContent = `
            #tm-float-ball, #tm-listener-ball {
                position: fixed;
                width: 80px;
                height: 80px;
                border-radius: 50%;
                cursor: pointer;
                z-index: 19891001;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
                user-select: none;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
            }

            #tm-float-ball {
                top: 62%;
                right: 5%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            #tm-listener-ball {
                top: 50%;
                right: 5%;
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }

            #tm-listener-ball.listening {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { box-shadow: 0 4px 20px rgba(0,0,0,0.3); }
                50% { box-shadow: 0 6px 30px rgba(79, 172, 254, 0.6); }
                100% { box-shadow: 0 4px 20px rgba(0,0,0,0.3); }
            }

            #tm-float-ball:hover, #tm-listener-ball:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 25px rgba(0,0,0,0.4);
            }

            #tm-float-ball:active, #tm-listener-ball:active {
                transform: scale(0.95);
            }

            .tm-float-ball-container {
                text-align: center;
                color: white;
            }

            .tm-float-ball-icon {
                font-size: 24px;
                margin-bottom: 2px;
            }

            .tm-float-ball-text {
                font-size: 10px;
                font-weight: bold;
                opacity: 0.9;
            }

            /* 拖拽时的样式 */
            #tm-float-ball.dragging, #tm-listener-ball.dragging {
                opacity: 0.8;
                transform: scale(1.05);
            }
        `;

        // 添加样式到页面
        document.head.appendChild(style);
    }

    // 切换监听状态
    function toggleListener() {
        const listenerBall = document.getElementById('tm-listener-ball');
        if (!listenerBall) return;

        if (isListening) {
            stopListening();
            listenerBall.classList.remove('listening');
            listenerBall.querySelector('.tm-float-ball-text').textContent = '监听接口';
            showNotification('已停止监听接口', 'info');
        } else {
            startListening();
            listenerBall.classList.add('listening');
            listenerBall.querySelector('.tm-float-ball-text').textContent = '监听中...';
            showNotification('开始监听querySubOrderList接口', 'success');
        }
    }

    // 开始监听接口
    function startListening() {
        if (isListening) return;

        isListening = true;
        console.log('[TM小助手] 开始监听接口...');

        // 方法1: 注入脚本到页面上下文中进行监听
        injectNetworkListener();

        // 方法2: 在content script中直接拦截 (备用方法)
        setupContentScriptInterceptors();

        // 方法3: 监听自定义事件
        setupCustomEventListener();

        // 方法4: 额外的监听方法
        startNetworkMonitoring();
    }

    // 注入网络监听脚本到页面上下文 (绕过CSP限制)
    function injectNetworkListener() {
        try {
            // 方法1: 尝试创建外部脚本文件
            const script = document.createElement('script');

            // 创建脚本内容的Blob URL来绕过CSP
            const scriptContent = `
                (function() {
                    console.log('[注入脚本] 开始执行网络监听');

                    const originalFetch = window.fetch;
                    const originalXHROpen = XMLHttpRequest.prototype.open;
                    const originalXHRSend = XMLHttpRequest.prototype.send;

                    // 创建通信函数
                    function notifyContentScript(data) {
                        const event = new CustomEvent('TM_NETWORK_RESPONSE', { detail: data });
                        document.dispatchEvent(event);
                        console.log('[注入脚本] 发送响应事件:', data);
                    }

                    // 拦截fetch
                    window.fetch = function(...args) {
                        const [url, options] = args;
                        console.log('[注入脚本] Fetch请求:', url);

                        return originalFetch.apply(this, args).then(response => {
                            if (url && url.includes('querySubOrderList')) {
                                console.log('[注入脚本] 检测到目标接口 (Fetch):', url);
                                const clonedResponse = response.clone();
                                clonedResponse.text().then(responseText => {
                                    console.log('[注入脚本] 获取响应 (Fetch):', responseText.substring(0, 100));
                                    notifyContentScript({
                                        type: 'fetch',
                                        url: url,
                                        response: responseText,
                                        timestamp: new Date().toISOString()
                                    });
                                });
                            }
                            return response;
                        });
                    };

                    // 拦截XHR
                    XMLHttpRequest.prototype.open = function(method, url, ...args) {
                        this._url = url;
                        console.log('[注入脚本] XHR请求:', method, url);
                        return originalXHROpen.apply(this, [method, url, ...args]);
                    };

                    XMLHttpRequest.prototype.send = function(...args) {
                        const xhr = this;
                        if (xhr._url && xhr._url.includes('querySubOrderList')) {
                            console.log('[注入脚本] 检测到目标接口 (XHR):', xhr._url);

                            xhr.addEventListener('load', function() {
                                if (this.status >= 200 && this.status < 300) {
                                    console.log('[注入脚本] 获取响应 (XHR):', this.responseText.substring(0, 100));
                                    notifyContentScript({
                                        type: 'xhr',
                                        url: this._url,
                                        response: this.responseText,
                                        timestamp: new Date().toISOString()
                                    });
                                }
                            });
                        }
                        return originalXHRSend.apply(this, args);
                    };

                    console.log('[注入脚本] 网络拦截器设置完成');
                })();
            `;

            // 尝试使用Blob URL绕过CSP
            try {
                const blob = new Blob([scriptContent], { type: 'application/javascript' });
                const blobUrl = URL.createObjectURL(blob);
                script.src = blobUrl;
                script.onload = () => {
                    URL.revokeObjectURL(blobUrl);
                    console.log('[TM小助手] 网络监听脚本已通过Blob URL注入');
                };
                script.onerror = () => {
                    URL.revokeObjectURL(blobUrl);
                    console.log('[TM小助手] Blob URL注入失败，尝试其他方法');
                    injectViaExtensionResource();
                };
                (document.head || document.documentElement).appendChild(script);
            } catch (e) {
                console.log('[TM小助手] Blob URL方法失败，尝试其他方法:', e);
                injectViaExtensionResource();
            }
        } catch (e) {
            console.error('[TM小助手] 注入脚本失败:', e);
            // CSP阻止了脚本注入，使用备用方法
            console.log('[TM小助手] 由于CSP限制，将使用备用监听方法');
        }
    }

    // 通过扩展资源注入脚本
    function injectViaExtensionResource() {
        try {
            // 创建一个指向扩展内部资源的脚本
            const script = document.createElement('script');
            script.src = chrome.runtime.getURL('content-scripts/network-injector.js');
            script.onload = () => {
                console.log('[TM小助手] 网络监听脚本已通过扩展资源注入');
            };
            script.onerror = () => {
                console.log('[TM小助手] 扩展资源注入失败，使用完全备用方法');
            };
            (document.head || document.documentElement).appendChild(script);
        } catch (e) {
            console.log('[TM小助手] 扩展资源方法也失败:', e);
        }
    }

    // 设置自定义事件监听器
    function setupCustomEventListener() {
        document.addEventListener('TM_NETWORK_RESPONSE', handleNetworkResponse);
        console.log('[TM小助手] 自定义事件监听器已设置');
    }

    // 在content script中设置拦截器 (备用方法)
    function setupContentScriptInterceptors() {
        // 这个方法作为备用，在某些情况下可能有效
        if (!originalFetch) {
            originalFetch = window.fetch;
            window.fetch = function(...args) {
                const [url, options] = args;
                console.log('[TM小助手] Content Script Fetch:', url);

                return originalFetch.apply(this, args).then(response => {
                    if (url && url.includes('querySubOrderList')) {
                        console.log('[TM小助手] Content Script检测到目标接口:', url);
                        const clonedResponse = response.clone();
                        clonedResponse.text().then(responseText => {
                            copyToClipboard(responseText, url);
                        });
                    }
                    return response;
                });
            };
        }
    }

    // 网络监听增强方法
    function startNetworkMonitoring() {
        // 方法1: 监听页面中可能触发网络请求的事件
        document.addEventListener('click', function(e) {
            setTimeout(() => {
                checkRecentNetworkActivity();
            }, 1000);
        }, true);

        // 方法2: 定期检查网络活动
        const networkCheckInterval = setInterval(() => {
            if (!isListening) {
                clearInterval(networkCheckInterval);
                return;
            }
            checkRecentNetworkActivity();
        }, 2000);

        // 方法3: 监听可能的AJAX库
        monitorAjaxLibraries();
    }

    // 检查最近的网络活动
    function checkRecentNetworkActivity() {
        if (!window.performance || !window.performance.getEntriesByType) return;

        const entries = window.performance.getEntriesByType('resource');
        const recentEntries = entries.filter(entry => {
            const timeSinceLoad = Date.now() - (performance.timing.navigationStart + entry.startTime);
            return timeSinceLoad < 5000; // 最近5秒内的请求
        });

        recentEntries.forEach(entry => {
            if (entry.name && entry.name.includes('querySubOrderList')) {
                console.log('[TM小助手] Performance检测到最近的目标接口:', entry.name);
                // 尝试通过多种方式获取响应
                tryGetResponseFromCache(entry.name);
                // 尝试通过DOM查找相关数据
                tryExtractDataFromDOM(entry.name);
            }
        });
    }

    // 尝试从DOM中提取相关数据
    function tryExtractDataFromDOM(url) {
        console.log('[TM小助手] 尝试从DOM提取数据:', url);

        // 方法1: 查找可能包含数据的script标签
        const scripts = document.querySelectorAll('script');
        for (let script of scripts) {
            if (script.textContent && script.textContent.includes('querySubOrderList')) {
                console.log('[TM小助手] 在script标签中找到相关数据');
                // 尝试提取JSON数据
                const jsonMatches = script.textContent.match(/\{[^{}]*"code"[^{}]*\}/g);
                if (jsonMatches) {
                    jsonMatches.forEach(match => {
                        try {
                            const data = JSON.parse(match);
                            if (data.code !== undefined) {
                                console.log('[TM小助手] 从DOM提取到数据:', match.substring(0, 100));
                                copyToClipboard(match, url);
                            }
                        } catch (e) {
                            // 忽略解析错误
                        }
                    });
                }
            }
        }

        // 方法2: 查找可能的数据容器
        try {
            const dataContainers = document.querySelectorAll('[data-response], .response-data, #response-data, .api-response');
            for (let container of dataContainers) {
                if (container.textContent && container.textContent.includes('querySubOrderList')) {
                    console.log('[TM小助手] 在数据容器中找到相关数据');
                    copyToClipboard(container.textContent, url);
                    break;
                }
            }
        } catch (e) {
            console.log('[TM小助手] 数据容器查询失败:', e);
        }

        // 方法3: 监听DOM变化，捕获动态插入的数据
        if (!window.tmDomObserver) {
            window.tmDomObserver = new MutationObserver(function(mutations) {
                mutations.forEach(function(mutation) {
                    if (mutation.type === 'childList') {
                        mutation.addedNodes.forEach(function(node) {
                            if (node.nodeType === Node.TEXT_NODE && node.textContent.includes('querySubOrderList')) {
                                console.log('[TM小助手] DOM变化检测到相关数据');
                                copyToClipboard(node.textContent, url);
                            }
                        });
                    }
                });
            });

            window.tmDomObserver.observe(document.body, {
                childList: true,
                subtree: true,
                characterData: true
            });

            // 5分钟后停止观察
            setTimeout(() => {
                if (window.tmDomObserver) {
                    window.tmDomObserver.disconnect();
                    window.tmDomObserver = null;
                }
            }, 300000);
        }
    }

    // 监听常见的AJAX库
    function monitorAjaxLibraries() {
        // 监听jQuery AJAX
        if (window.jQuery && window.jQuery.ajaxSetup) {
            try {
                const originalAjax = window.jQuery.ajax;
                window.jQuery.ajax = function(options) {
                    if (options.url && options.url.includes('querySubOrderList')) {
                        console.log('[TM小助手] jQuery AJAX检测到目标接口:', options.url);
                        const originalSuccess = options.success;
                        options.success = function(data, textStatus, jqXHR) {
                            console.log('[TM小助手] jQuery AJAX响应:', data);
                            copyToClipboard(typeof data === 'string' ? data : JSON.stringify(data), options.url);
                            if (originalSuccess) originalSuccess.apply(this, arguments);
                        };
                    }
                    return originalAjax.apply(this, arguments);
                };
                console.log('[TM小助手] jQuery AJAX监听已设置');
            } catch (e) {
                console.log('[TM小助手] jQuery AJAX监听设置失败:', e);
            }
        }

        // 监听axios
        if (window.axios && window.axios.interceptors) {
            try {
                window.axios.interceptors.response.use(
                    function (response) {
                        if (response.config.url && response.config.url.includes('querySubOrderList')) {
                            console.log('[TM小助手] Axios检测到目标接口响应:', response.config.url);
                            copyToClipboard(JSON.stringify(response.data), response.config.url);
                        }
                        return response;
                    },
                    function (error) {
                        return Promise.reject(error);
                    }
                );
                console.log('[TM小助手] Axios拦截器已设置');
            } catch (e) {
                console.log('[TM小助手] Axios拦截器设置失败:', e);
            }
        }
    }

    // 尝试从缓存获取响应
    function tryGetResponseFromCache(url) {
        console.log('[TM小助手] 尝试从缓存获取响应:', url);

        // 方法1: 尝试通过background script获取响应
        if (typeof chrome !== 'undefined' && chrome.runtime) {
            chrome.runtime.sendMessage({
                action: 'getNetworkResponse',
                url: url
            }, (response) => {
                if (response && response.data) {
                    console.log('[TM小助手] 从background获取到响应:', response.data.substring(0, 100));
                    copyToClipboard(response.data, url);
                }
            });
        }

        // 方法2: 尝试重新发起请求获取响应
        tryRefetchResponse(url);
    }

    // 尝试重新发起请求获取响应 (简化版本)
    function tryRefetchResponse(url) {
        console.log('[TM小助手] 尝试重新获取响应:', url);

        // 由于CSP限制，iframe方法不可用
        // 改为监听页面中可能的数据更新
        setTimeout(() => {
            tryExtractDataFromDOM(url);
        }, 1000);

        // 尝试通过fetch获取（可能会被CORS阻止，但值得一试）
        try {
            fetch(url, {
                method: 'GET',
                credentials: 'include'
            }).then(response => {
                if (response.ok) {
                    return response.text();
                }
                throw new Error('Response not ok');
            }).then(data => {
                console.log('[TM小助手] 通过fetch获取到响应:', data.substring(0, 100));
                copyToClipboard(data, url);
            }).catch(err => {
                console.log('[TM小助手] fetch重新请求失败:', err.message);
            });
        } catch (e) {
            console.log('[TM小助手] fetch方法不可用:', e);
        }
    }

    // 停止监听接口
    function stopListening() {
        if (!isListening) return;

        isListening = false;
        console.log('[TM小助手] 停止监听接口');

        // 移除自定义事件监听器
        document.removeEventListener('TM_NETWORK_RESPONSE', handleNetworkResponse);

        // 恢复原始方法 (如果有的话)
        if (originalFetch) {
            window.fetch = originalFetch;
            originalFetch = null;
        }

        if (originalXHROpen && originalXHRSend) {
            XMLHttpRequest.prototype.open = originalXHROpen;
            XMLHttpRequest.prototype.send = originalXHRSend;
            originalXHROpen = null;
            originalXHRSend = null;
        }

        // 注意: 注入到页面的脚本无法完全移除，但会通过isListening状态控制
    }

    // 处理网络响应的函数
    function handleNetworkResponse(event) {
        if (!isListening) return;

        const data = event.detail;
        console.log('[TM小助手] 收到网络响应事件:', data);
        copyToClipboard(data.response, data.url);
    }

    // 复制到剪切板
    function copyToClipboard(text, url) {
        try {
            // 格式化数据
            const timestamp = new Date().toLocaleString('zh-CN');
            const formattedData = {
                timestamp: timestamp,
                url: url,
                response: JSON.parse(text)
            };

            const formattedText = JSON.stringify(formattedData, null, 2);

            // 使用现代剪切板API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(formattedText).then(() => {
                    showNotification('接口响应已复制到剪切板', 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopyToClipboard(formattedText);
                });
            } else {
                fallbackCopyToClipboard(formattedText);
            }
        } catch (error) {
            console.error('处理响应数据失败:', error);
            // 如果JSON解析失败，直接复制原始文本
            const simpleData = `时间: ${new Date().toLocaleString('zh-CN')}\nURL: ${url}\n响应: ${text}`;
            fallbackCopyToClipboard(simpleData);
        }
    }

    // 备用复制方法
    function fallbackCopyToClipboard(text) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);

            if (successful) {
                showNotification('接口响应已复制到剪切板', 'success');
            } else {
                showNotification('复制失败，请手动复制', 'error');
            }
        } catch (err) {
            console.error('备用复制方法失败:', err);
            showNotification('复制失败，请手动复制', 'error');
        }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        // 移除已存在的通知
        const existingNotification = document.getElementById('tm-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = 'tm-notification';
        notification.textContent = message;

        // 设置样式
        const bgColor = type === 'success' ? '#67c23a' :
                       type === 'error' ? '#f56c6c' :
                       type === 'warning' ? '#e6a23c' : '#409eff';

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: ${bgColor};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 19891002;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 清理弹窗函数
    function clearPopups() {
        try {
            // 清理各种类型的弹窗和遮罩
            const selectors = [
                '[data-testid=beast-core-modal-mask]',
                '[class*="MDL_mask"]',
                '[data-testid=beast-core-modal]',
                '[class*="MDL_outerWrapper"]',
                '[class*="MDL_modal"]',
                '.modal-mask',
                '.modal-wrapper',
                '.el-overlay',
                '.ant-modal-mask',
                '.ant-modal-wrap',
                '[class*="Modal"]',
                '[class*="popup"]',
                '[class*="dialog"]'
            ];

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element && element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                });
            });

        } catch (error) {
            console.error('清理弹窗时出错:', error);
        }
    }

    
    // 拖拽功能
    function makeDraggable(element) {
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        
        element.addEventListener('mousedown', function(e) {
            isDragging = true;
            element.classList.add('dragging');
            
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(window.getComputedStyle(element).right);
            startTop = parseInt(window.getComputedStyle(element).top);
            
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            
            const deltaX = startX - e.clientX;
            const deltaY = e.clientY - startY;
            
            const newRight = startLeft + deltaX;
            const newTop = startTop + deltaY;
            
            // 限制在窗口范围内
            const maxRight = window.innerWidth - element.offsetWidth;
            const maxTop = window.innerHeight - element.offsetHeight;
            
            element.style.right = Math.max(0, Math.min(newRight, maxRight)) + 'px';
            element.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
        });
        
        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                element.classList.remove('dragging');
            }
        });
    }
    
    // 初始化所有悬浮球
    function initializeFloatBalls() {
        addStyles();
        createFloatBall();
        createListenerBall();
    }

    // 监听来自background的消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
            if (message.action === 'handFloatBall') {
                if (message.floatBallShow) {
                    initializeFloatBalls();
                } else {
                    // 移除所有悬浮球
                    const floatBall = document.getElementById('tm-float-ball');
                    const listenerBall = document.getElementById('tm-listener-ball');
                    if (floatBall) floatBall.remove();
                    if (listenerBall) listenerBall.remove();

                    // 停止监听
                    if (isListening) {
                        stopListening();
                        isListening = false;
                    }
                }
            } else if (message.action === 'injectAutoAddDelivery') {
                // 自动清理弹窗
                clearPopups();
            }
        });
    }

    // 页面加载完成后创建悬浮球
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeFloatBalls);
    } else {
        initializeFloatBalls();
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        if (isListening) {
            stopListening();
        }
    });
    
})();
