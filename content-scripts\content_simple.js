// TM小助手 - 悬浮球功能
(function() {
    'use strict';

    // 全局状态
    let isListening = false;
    let originalFetch = null;
    let originalXHROpen = null;
    let originalXHRSend = null;

    // 创建清理弹窗悬浮球
    function createFloatBall() {
        // 检查是否已存在悬浮球
        if (document.getElementById('tm-float-ball')) {
            return;
        }

        // 创建悬浮球容器
        const floatBall = document.createElement('div');
        floatBall.id = 'tm-float-ball';
        floatBall.innerHTML = `
            <div class="tm-float-ball-container">
                <div class="tm-float-ball-icon">🧹</div>
                <div class="tm-float-ball-text">清理弹窗</div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(floatBall);

        // 添加拖拽功能
        makeDraggable(floatBall);

        // 添加点击事件 - 直接清理弹窗
        floatBall.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            clearPopups();
        });
    }

    // 创建监听接口悬浮球
    function createListenerBall() {
        // 检查是否已存在监听球
        if (document.getElementById('tm-listener-ball')) {
            return;
        }

        // 创建监听球容器
        const listenerBall = document.createElement('div');
        listenerBall.id = 'tm-listener-ball';
        listenerBall.innerHTML = `
            <div class="tm-float-ball-container">
                <div class="tm-float-ball-icon">📋</div>
                <div class="tm-float-ball-text">监听接口</div>
            </div>
        `;

        // 添加到页面
        document.body.appendChild(listenerBall);

        // 添加拖拽功能
        makeDraggable(listenerBall);

        // 添加点击事件 - 切换监听状态
        listenerBall.addEventListener('click', function(e) {
            e.preventDefault();
            e.stopPropagation();
            toggleListener();
        });
    }

    // 添加样式
    function addStyles() {
        if (document.getElementById('tm-float-styles')) {
            return;
        }

        const style = document.createElement('style');
        style.id = 'tm-float-styles';
        style.textContent = `
            #tm-float-ball, #tm-listener-ball {
                position: fixed;
                width: 80px;
                height: 80px;
                border-radius: 50%;
                cursor: pointer;
                z-index: 19891001;
                box-shadow: 0 4px 20px rgba(0,0,0,0.3);
                transition: all 0.3s ease;
                user-select: none;
                display: flex;
                align-items: center;
                justify-content: center;
                flex-direction: column;
            }

            #tm-float-ball {
                top: 62%;
                right: 5%;
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            }

            #tm-listener-ball {
                top: 50%;
                right: 5%;
                background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            }

            #tm-listener-ball.listening {
                background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
                animation: pulse 2s infinite;
            }

            @keyframes pulse {
                0% { box-shadow: 0 4px 20px rgba(0,0,0,0.3); }
                50% { box-shadow: 0 6px 30px rgba(79, 172, 254, 0.6); }
                100% { box-shadow: 0 4px 20px rgba(0,0,0,0.3); }
            }

            #tm-float-ball:hover, #tm-listener-ball:hover {
                transform: scale(1.1);
                box-shadow: 0 6px 25px rgba(0,0,0,0.4);
            }

            #tm-float-ball:active, #tm-listener-ball:active {
                transform: scale(0.95);
            }

            .tm-float-ball-container {
                text-align: center;
                color: white;
            }

            .tm-float-ball-icon {
                font-size: 24px;
                margin-bottom: 2px;
            }

            .tm-float-ball-text {
                font-size: 10px;
                font-weight: bold;
                opacity: 0.9;
            }

            /* 拖拽时的样式 */
            #tm-float-ball.dragging, #tm-listener-ball.dragging {
                opacity: 0.8;
                transform: scale(1.05);
            }
        `;

        // 添加样式到页面
        document.head.appendChild(style);
    }

    // 切换监听状态
    function toggleListener() {
        const listenerBall = document.getElementById('tm-listener-ball');
        if (!listenerBall) return;

        if (isListening) {
            stopListening();
            listenerBall.classList.remove('listening');
            listenerBall.querySelector('.tm-float-ball-text').textContent = '监听接口';
            showNotification('已停止监听接口', 'info');
        } else {
            startListening();
            listenerBall.classList.add('listening');
            listenerBall.querySelector('.tm-float-ball-text').textContent = '监听中...';
            showNotification('开始监听querySubOrderList接口', 'success');
        }
    }

    // 开始监听接口
    function startListening() {
        if (isListening) return;

        isListening = true;

        // 拦截fetch请求
        if (!originalFetch) {
            originalFetch = window.fetch;
            window.fetch = function(...args) {
                const [url, options] = args;

                return originalFetch.apply(this, args).then(response => {
                    // 检查是否是目标接口
                    if (url && url.includes('querySubOrderList')) {
                        // 克隆响应以便读取
                        const clonedResponse = response.clone();
                        clonedResponse.text().then(responseText => {
                            copyToClipboard(responseText, url);
                        }).catch(err => {
                            console.error('读取响应失败:', err);
                        });
                    }
                    return response;
                });
            };
        }

        // 拦截XMLHttpRequest
        if (!originalXHROpen) {
            originalXHROpen = XMLHttpRequest.prototype.open;
            originalXHRSend = XMLHttpRequest.prototype.send;

            XMLHttpRequest.prototype.open = function(method, url, ...args) {
                this._url = url;
                return originalXHROpen.apply(this, [method, url, ...args]);
            };

            XMLHttpRequest.prototype.send = function(...args) {
                if (this._url && this._url.includes('querySubOrderList')) {
                    this.addEventListener('load', function() {
                        if (this.status >= 200 && this.status < 300) {
                            copyToClipboard(this.responseText, this._url);
                        }
                    });
                }
                return originalXHRSend.apply(this, args);
            };
        }
    }

    // 停止监听接口
    function stopListening() {
        if (!isListening) return;

        isListening = false;

        // 恢复原始fetch
        if (originalFetch) {
            window.fetch = originalFetch;
        }

        // 恢复原始XMLHttpRequest
        if (originalXHROpen && originalXHRSend) {
            XMLHttpRequest.prototype.open = originalXHROpen;
            XMLHttpRequest.prototype.send = originalXHRSend;
        }
    }

    // 复制到剪切板
    function copyToClipboard(text, url) {
        try {
            // 格式化数据
            const timestamp = new Date().toLocaleString('zh-CN');
            const formattedData = {
                timestamp: timestamp,
                url: url,
                response: JSON.parse(text)
            };

            const formattedText = JSON.stringify(formattedData, null, 2);

            // 使用现代剪切板API
            if (navigator.clipboard && navigator.clipboard.writeText) {
                navigator.clipboard.writeText(formattedText).then(() => {
                    showNotification('接口响应已复制到剪切板', 'success');
                }).catch(err => {
                    console.error('复制失败:', err);
                    fallbackCopyToClipboard(formattedText);
                });
            } else {
                fallbackCopyToClipboard(formattedText);
            }
        } catch (error) {
            console.error('处理响应数据失败:', error);
            // 如果JSON解析失败，直接复制原始文本
            const simpleData = `时间: ${new Date().toLocaleString('zh-CN')}\nURL: ${url}\n响应: ${text}`;
            fallbackCopyToClipboard(simpleData);
        }
    }

    // 备用复制方法
    function fallbackCopyToClipboard(text) {
        try {
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();

            const successful = document.execCommand('copy');
            document.body.removeChild(textArea);

            if (successful) {
                showNotification('接口响应已复制到剪切板', 'success');
            } else {
                showNotification('复制失败，请手动复制', 'error');
            }
        } catch (err) {
            console.error('备用复制方法失败:', err);
            showNotification('复制失败，请手动复制', 'error');
        }
    }

    // 显示通知
    function showNotification(message, type = 'info') {
        // 移除已存在的通知
        const existingNotification = document.getElementById('tm-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        // 创建通知元素
        const notification = document.createElement('div');
        notification.id = 'tm-notification';
        notification.textContent = message;

        // 设置样式
        const bgColor = type === 'success' ? '#67c23a' :
                       type === 'error' ? '#f56c6c' :
                       type === 'warning' ? '#e6a23c' : '#409eff';

        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background-color: ${bgColor};
            color: white;
            padding: 12px 20px;
            border-radius: 6px;
            font-size: 14px;
            font-weight: 500;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            z-index: 19891002;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;

        document.body.appendChild(notification);

        // 显示动画
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);

        // 自动隐藏
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 300);
        }, 3000);
    }

    // 清理弹窗函数
    function clearPopups() {
        try {
            // 清理各种类型的弹窗和遮罩
            const selectors = [
                '[data-testid=beast-core-modal-mask]',
                '[class*="MDL_mask"]',
                '[data-testid=beast-core-modal]',
                '[class*="MDL_outerWrapper"]',
                '[class*="MDL_modal"]',
                '.modal-mask',
                '.modal-wrapper',
                '.el-overlay',
                '.ant-modal-mask',
                '.ant-modal-wrap',
                '[class*="Modal"]',
                '[class*="popup"]',
                '[class*="dialog"]'
            ];

            selectors.forEach(selector => {
                const elements = document.querySelectorAll(selector);
                elements.forEach(element => {
                    if (element && element.parentNode) {
                        element.parentNode.removeChild(element);
                    }
                });
            });

        } catch (error) {
            console.error('清理弹窗时出错:', error);
        }
    }

    
    // 拖拽功能
    function makeDraggable(element) {
        let isDragging = false;
        let startX, startY, startLeft, startTop;
        
        element.addEventListener('mousedown', function(e) {
            isDragging = true;
            element.classList.add('dragging');
            
            startX = e.clientX;
            startY = e.clientY;
            startLeft = parseInt(window.getComputedStyle(element).right);
            startTop = parseInt(window.getComputedStyle(element).top);
            
            e.preventDefault();
        });
        
        document.addEventListener('mousemove', function(e) {
            if (!isDragging) return;
            
            const deltaX = startX - e.clientX;
            const deltaY = e.clientY - startY;
            
            const newRight = startLeft + deltaX;
            const newTop = startTop + deltaY;
            
            // 限制在窗口范围内
            const maxRight = window.innerWidth - element.offsetWidth;
            const maxTop = window.innerHeight - element.offsetHeight;
            
            element.style.right = Math.max(0, Math.min(newRight, maxRight)) + 'px';
            element.style.top = Math.max(0, Math.min(newTop, maxTop)) + 'px';
        });
        
        document.addEventListener('mouseup', function() {
            if (isDragging) {
                isDragging = false;
                element.classList.remove('dragging');
            }
        });
    }
    
    // 初始化所有悬浮球
    function initializeFloatBalls() {
        addStyles();
        createFloatBall();
        createListenerBall();
    }

    // 监听来自background的消息
    if (typeof chrome !== 'undefined' && chrome.runtime) {
        chrome.runtime.onMessage.addListener(function(message, sender, sendResponse) {
            if (message.action === 'handFloatBall') {
                if (message.floatBallShow) {
                    initializeFloatBalls();
                } else {
                    // 移除所有悬浮球
                    const floatBall = document.getElementById('tm-float-ball');
                    const listenerBall = document.getElementById('tm-listener-ball');
                    if (floatBall) floatBall.remove();
                    if (listenerBall) listenerBall.remove();

                    // 停止监听
                    if (isListening) {
                        stopListening();
                        isListening = false;
                    }
                }
            } else if (message.action === 'injectAutoAddDelivery') {
                // 自动清理弹窗
                clearPopups();
            }
        });
    }

    // 页面加载完成后创建悬浮球
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeFloatBalls);
    } else {
        initializeFloatBalls();
    }

    // 页面卸载时清理
    window.addEventListener('beforeunload', function() {
        if (isListening) {
            stopListening();
        }
    });
    
})();
