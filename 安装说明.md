# TM小助手 - 安装和使用说明

## 📦 安装方法

### 方法一：开发者模式安装（推荐）
1. 打开 Chrome 浏览器
2. 地址栏输入 `chrome://extensions/`
3. 开启右上角的"开发者模式"
4. 点击"加载已解压的扩展程序"
5. 选择插件文件夹 `1.0.5_0`
6. 安装完成！

### 方法二：打包安装
1. 在扩展程序页面点击"打包扩展程序"
2. 选择插件文件夹，生成 `.crx` 文件
3. 拖拽 `.crx` 文件到扩展程序页面安装

## 🎯 功能使用

### 🧹 清理弹窗功能
- **位置**：页面右侧 62% 高度处
- **图标**：🧹 (扫帚图标)
- **颜色**：紫色渐变
- **操作**：点击即可清理页面上的所有弹窗

### 📋 接口监听功能 (新增)
- **位置**：页面右侧 50% 高度处  
- **图标**：📋 (剪贴板图标)
- **颜色**：粉色渐变 (待机) / 蓝色渐变 (监听中)
- **操作**：
  - 点击开始监听 `querySubOrderList` 接口
  - 监听到响应后自动复制到剪切板
  - 再次点击停止监听

## 🌐 适用网站

插件会在以下网站自动激活：
- `*.kuajingmaihuo.com/*`
- `agentseller-eu.temu.com/*`
- `agentseller-us.temu.com/*`
- `agentseller.temu.com/*`

## 🎮 操作指南

### 基本操作
1. **拖拽移动**：两个悬浮球都支持鼠标拖拽移动位置
2. **悬停效果**：鼠标悬停时悬浮球会放大
3. **点击反馈**：点击时有缩小动画效果

### 监听接口操作
1. **开始监听**：
   - 点击 📋 悬浮球
   - 悬浮球变蓝色并显示"监听中..."
   - 右上角显示"开始监听"通知

2. **数据复制**：
   - 监听状态下访问包含 `querySubOrderList` 的接口
   - 响应数据自动复制到剪切板
   - 显示"接口响应已复制到剪切板"通知

3. **停止监听**：
   - 再次点击 📋 悬浮球
   - 悬浮球恢复粉色并显示"监听接口"
   - 显示"已停止监听接口"通知

### 复制的数据格式
```json
{
  "timestamp": "2024-01-01 12:00:00",
  "url": "完整的请求URL",
  "response": {
    // 接口返回的JSON数据
  }
}
```

## 🔧 故障排除

### 悬浮球不显示
1. 检查是否在支持的网站上
2. 刷新页面重试
3. 检查浏览器控制台是否有错误

### 监听功能不工作
1. 确认已点击开始监听（悬浮球变蓝色）
2. 检查接口URL是否包含 `querySubOrderList`
3. 确认浏览器支持剪切板API

### 复制失败
1. 现代浏览器会自动使用 `navigator.clipboard` API
2. 旧版浏览器会降级到 `document.execCommand`
3. 如果都失败，请检查浏览器权限设置

## 🛡️ 隐私说明

- 插件只在指定的Temu相关网站运行
- 不收集或上传任何用户数据
- 监听的接口数据仅复制到本地剪切板
- 不修改原始的网络请求和响应

## 📞 技术支持

如遇到问题，请检查：
1. 浏览器版本是否过旧
2. 是否在支持的网站上使用
3. 浏览器控制台是否有错误信息

## 🔄 版本更新

### v1.0.6 (当前版本)
- ✅ 新增接口监听悬浮球
- ✅ 实现 querySubOrderList 接口监听
- ✅ 添加剪切板复制功能
- ✅ 优化通知系统
- ✅ 完善错误处理

### v1.0.5 (之前版本)
- ✅ 基础弹窗清理功能
- ✅ 悬浮球拖拽功能
- ✅ 多种弹窗类型支持

## 💡 使用技巧

1. **位置调整**：可以拖拽悬浮球到合适的位置，不会遮挡操作
2. **快速清理**：遇到弹窗干扰时，直接点击🧹悬浮球即可
3. **数据收集**：需要收集接口数据时，先开启监听再进行操作
4. **批量操作**：监听状态下可以连续操作，每次响应都会被复制
