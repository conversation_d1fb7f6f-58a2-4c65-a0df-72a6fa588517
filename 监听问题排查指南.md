# TM小助手 - 接口监听问题排查指南

## 🔍 问题诊断步骤

### 1. 基础检查
- [ ] 确认插件已正确安装并启用
- [ ] 确认页面右侧显示两个悬浮球（🧹 和 📋）
- [ ] 确认在支持的网站上使用（Temu相关域名）
- [ ] 刷新页面重试

### 2. 监听状态检查
- [ ] 点击📋悬浮球，确认变为蓝色
- [ ] 查看浏览器控制台是否有 `[TM小助手] 开始监听接口...` 日志
- [ ] 确认显示 `[TM小助手] 网络监听脚本已注入` 消息

### 3. 网络请求检查
- [ ] 打开浏览器开发者工具 (F12)
- [ ] 切换到 Network/网络 标签页
- [ ] 执行包含 `querySubOrderList` 的操作
- [ ] 确认在网络面板中看到相关请求

### 4. 控制台日志检查
在浏览器控制台中查找以下关键日志：

#### 正常工作的日志序列：
```
[TM小助手] 开始监听接口...
[TM小助手] 网络监听脚本已注入
[注入脚本] 开始执行网络监听
[注入脚本] 网络拦截器设置完成
[TM小助手] 自定义事件监听器已设置
```

#### 检测到请求时的日志：
```
[注入脚本] Fetch请求: /api/querySubOrderList
[注入脚本] 检测到目标接口 (Fetch): /api/querySubOrderList
[注入脚本] 获取响应 (Fetch): {"code":0,"data":...
[注入脚本] 发送响应事件: {type: "fetch", url: "...", response: "..."}
[TM小助手] 收到网络响应事件: {type: "fetch", ...}
[TM小助手] 获取到响应数据 (Fetch): {"code":0...
接口响应已复制到剪切板
```

## 🛠️ 修复方案

### 方案1: 脚本注入增强
如果注入脚本失败，尝试以下修复：

1. **检查CSP策略**
   - 某些网站可能有严格的内容安全策略
   - 在控制台查看是否有CSP相关错误

2. **手动注入测试**
   ```javascript
   // 在控制台手动执行以下代码测试
   const script = document.createElement('script');
   script.textContent = 'console.log("手动注入测试成功");';
   document.head.appendChild(script);
   ```

### 方案2: 多重拦截策略
当前版本使用了4种监听方法：

1. **注入脚本监听** (主要方法)
2. **Content Script拦截** (备用方法)
3. **自定义事件通信** (通信桥梁)
4. **Performance API监听** (辅助监听)

### 方案3: 调试模式
使用调试页面进行详细测试：

1. 打开 `简单测试页面.html`
2. 按步骤操作并观察日志
3. 使用测试按钮模拟请求
4. 检查剪切板内容

## 🔧 常见问题解决

### 问题1: 悬浮球不显示
**解决方案：**
- 检查是否在支持的域名上
- 刷新页面
- 检查浏览器控制台错误

### 问题2: 监听状态不变蓝
**解决方案：**
- 检查控制台是否有脚本注入错误
- 尝试禁用其他可能冲突的扩展
- 清除浏览器缓存重试

### 问题3: 能看到请求但不复制
**解决方案：**
- 检查URL是否包含 `querySubOrderList`
- 确认剪切板权限已授予
- 查看控制台是否有复制相关错误

### 问题4: 复制的数据格式错误
**解决方案：**
- 检查响应是否为有效JSON
- 查看控制台中的响应数据日志
- 确认网络请求返回了预期数据

## 📋 手动测试步骤

### 测试环境准备
1. 打开 Chrome 浏览器
2. 加载插件到开发者模式
3. 访问测试页面或目标网站

### 功能测试流程
1. **基础功能测试**
   ```
   1. 确认悬浮球显示
   2. 点击📋开始监听
   3. 确认状态变化（蓝色+脉冲）
   4. 查看控制台日志
   ```

2. **接口监听测试**
   ```
   1. 执行包含querySubOrderList的操作
   2. 观察控制台日志输出
   3. 检查剪切板内容
   4. 验证数据格式正确性
   ```

3. **异常情况测试**
   ```
   1. 测试非目标接口（应该被忽略）
   2. 测试网络错误情况
   3. 测试大量数据的处理
   ```

## 🚨 紧急修复方案

如果所有方法都失败，可以尝试以下紧急方案：

### 方案A: 简化版监听
```javascript
// 在控制台直接执行
(function() {
    const originalFetch = window.fetch;
    window.fetch = function(...args) {
        const [url] = args;
        if (url && url.includes('querySubOrderList')) {
            console.log('检测到目标接口:', url);
        }
        return originalFetch.apply(this, args);
    };
})();
```

### 方案B: 手动复制
1. 打开开发者工具
2. 切换到Network标签
3. 找到querySubOrderList请求
4. 右键 → Copy → Copy Response

## 📞 技术支持

如果问题仍然存在，请提供以下信息：

1. **浏览器信息**
   - 浏览器版本
   - 操作系统版本

2. **错误日志**
   - 控制台完整错误信息
   - 网络请求详情

3. **复现步骤**
   - 详细的操作步骤
   - 预期结果 vs 实际结果

4. **环境信息**
   - 目标网站URL
   - 其他安装的扩展程序
