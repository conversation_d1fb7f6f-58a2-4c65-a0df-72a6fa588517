// 网络监听注入脚本 - 独立文件版本
(function() {
    'use strict';
    
    console.log('[网络注入器] 开始执行网络监听');
    
    const originalFetch = window.fetch;
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    // 创建通信函数
    function notifyContentScript(data) {
        const event = new CustomEvent('TM_NETWORK_RESPONSE', { detail: data });
        document.dispatchEvent(event);
        console.log('[网络注入器] 发送响应事件:', data);
    }
    
    // 拦截fetch请求
    window.fetch = function(...args) {
        const [url, options] = args;
        console.log('[网络注入器] Fetch请求:', url);
        
        return originalFetch.apply(this, args).then(response => {
            if (url && url.includes('querySubOrderList')) {
                console.log('[网络注入器] 检测到目标接口 (Fetch):', url);
                const clonedResponse = response.clone();
                clonedResponse.text().then(responseText => {
                    console.log('[网络注入器] 获取响应 (Fetch):', responseText.substring(0, 100) + '...');
                    notifyContentScript({
                        type: 'fetch',
                        url: url,
                        response: responseText,
                        timestamp: new Date().toISOString()
                    });
                }).catch(err => {
                    console.error('[网络注入器] 读取Fetch响应失败:', err);
                });
            }
            return response;
        }).catch(err => {
            console.error('[网络注入器] Fetch请求失败:', err);
            throw err;
        });
    };
    
    // 拦截XMLHttpRequest
    XMLHttpRequest.prototype.open = function(method, url, ...args) {
        this._url = url;
        this._method = method;
        console.log('[网络注入器] XHR请求:', method, url);
        return originalXHROpen.apply(this, [method, url, ...args]);
    };
    
    XMLHttpRequest.prototype.send = function(...args) {
        const xhr = this;
        if (xhr._url && xhr._url.includes('querySubOrderList')) {
            console.log('[网络注入器] 检测到目标接口 (XHR):', xhr._url);
            
            // 添加多个事件监听器确保捕获响应
            xhr.addEventListener('load', function() {
                if (this.status >= 200 && this.status < 300) {
                    console.log('[网络注入器] 获取响应 (XHR):', this.responseText.substring(0, 100) + '...');
                    notifyContentScript({
                        type: 'xhr',
                        url: this._url,
                        response: this.responseText,
                        timestamp: new Date().toISOString()
                    });
                }
            });
            
            xhr.addEventListener('readystatechange', function() {
                if (this.readyState === 4 && this.status >= 200 && this.status < 300) {
                    console.log('[网络注入器] XHR readyState=4 响应:', this.responseText.substring(0, 100) + '...');
                    notifyContentScript({
                        type: 'xhr-readystate',
                        url: this._url,
                        response: this.responseText,
                        timestamp: new Date().toISOString()
                    });
                }
            });
        }
        return originalXHRSend.apply(this, args);
    };
    
    // 监听jQuery AJAX (如果存在)
    if (window.jQuery && window.jQuery.ajaxSetup) {
        try {
            const originalAjax = window.jQuery.ajax;
            window.jQuery.ajax = function(options) {
                if (options.url && options.url.includes('querySubOrderList')) {
                    console.log('[网络注入器] jQuery AJAX检测到目标接口:', options.url);
                    
                    const originalSuccess = options.success;
                    options.success = function(data, textStatus, jqXHR) {
                        console.log('[网络注入器] jQuery AJAX响应:', data);
                        notifyContentScript({
                            type: 'jquery',
                            url: options.url,
                            response: typeof data === 'string' ? data : JSON.stringify(data),
                            timestamp: new Date().toISOString()
                        });
                        
                        if (originalSuccess) {
                            originalSuccess.apply(this, arguments);
                        }
                    };
                }
                return originalAjax.apply(this, arguments);
            };
            console.log('[网络注入器] jQuery AJAX拦截器已设置');
        } catch (e) {
            console.log('[网络注入器] jQuery AJAX拦截器设置失败:', e);
        }
    }
    
    // 监听axios (如果存在)
    if (window.axios && window.axios.interceptors) {
        try {
            window.axios.interceptors.response.use(
                function (response) {
                    if (response.config.url && response.config.url.includes('querySubOrderList')) {
                        console.log('[网络注入器] Axios检测到目标接口响应:', response.config.url);
                        notifyContentScript({
                            type: 'axios',
                            url: response.config.url,
                            response: JSON.stringify(response.data),
                            timestamp: new Date().toISOString()
                        });
                    }
                    return response;
                },
                function (error) {
                    return Promise.reject(error);
                }
            );
            console.log('[网络注入器] Axios拦截器已设置');
        } catch (e) {
            console.log('[网络注入器] Axios拦截器设置失败:', e);
        }
    }
    
    console.log('[网络注入器] 所有网络拦截器设置完成');
    
})();
