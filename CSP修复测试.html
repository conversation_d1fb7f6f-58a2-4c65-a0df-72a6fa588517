<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TM小助手 - CSP修复测试</title>
    <style>
        body {
            font-family: 'Microsoft YaHei', sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .status-section {
            margin-bottom: 30px;
            padding: 20px;
            border: 1px solid #e0e0e0;
            border-radius: 8px;
        }
        
        .status {
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        
        button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
            font-weight: 500;
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0,0,0,0.2);
        }
        
        .log {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            margin-top: 15px;
            max-height: 300px;
            overflow-y: auto;
            font-family: 'Courier New', monospace;
            font-size: 12px;
        }
        
        .solution-box {
            background: #e7f3ff;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin: 15px 0;
        }
        
        .solution-box h3 {
            margin-top: 0;
            color: #007bff;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 3px;
            padding: 10px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin: 10px 0;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TM小助手 - CSP限制修复测试</h1>
        
        <div class="status-section">
            <h2>📊 当前状态检测</h2>
            <div id="cspStatus" class="status info">检测中...</div>
            <div id="injectionStatus" class="status info">检测中...</div>
            <div id="performanceStatus" class="status info">检测中...</div>
            <button onclick="checkAllStatus()">重新检测</button>
        </div>
        
        <div class="status-section">
            <h2>🚀 解决方案测试</h2>
            <p>由于CSP限制了内联脚本执行，我们使用以下备用方案：</p>
            
            <div class="solution-box">
                <h3>方案1: 外部脚本文件注入</h3>
                <p>通过chrome.runtime.getURL()加载独立的脚本文件</p>
                <button onclick="testExternalScript()">测试外部脚本</button>
                <div id="externalScriptResult"></div>
            </div>
            
            <div class="solution-box">
                <h3>方案2: Performance API监听</h3>
                <p>利用Performance API检测网络请求，然后尝试获取响应</p>
                <button onclick="testPerformanceAPI()">测试Performance API</button>
                <div id="performanceResult"></div>
            </div>
            
            <div class="solution-box">
                <h3>方案3: DOM数据提取</h3>
                <p>从页面DOM中查找可能包含响应数据的元素</p>
                <button onclick="testDOMExtraction()">测试DOM提取</button>
                <div id="domResult"></div>
            </div>
            
            <div class="solution-box">
                <h3>方案4: 手动复制指导</h3>
                <p>当自动方法失败时，指导用户手动复制</p>
                <button onclick="showManualInstructions()">显示手动方法</button>
                <div id="manualInstructions"></div>
            </div>
        </div>
        
        <div class="status-section">
            <h2>📝 测试日志</h2>
            <button onclick="clearLog()">清空日志</button>
            <div class="log" id="testLog">等待测试操作...</div>
        </div>
    </div>

    <script>
        // 加载内容脚本
        const script = document.createElement('script');
        script.src = 'content-scripts/content_simple.js';
        document.head.appendChild(script);
        
        // 重写console.log来捕获调试信息
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        console.log = function(...args) {
            originalConsoleLog.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('[TM小助手]')) {
                logMessage('🔍 ' + args.join(' '));
            }
        };
        
        console.error = function(...args) {
            originalConsoleError.apply(console, args);
            if (args[0] && args[0].includes && args[0].includes('[TM小助手]')) {
                logMessage('❌ ' + args.join(' '));
            }
        };
        
        // 检查所有状态
        function checkAllStatus() {
            checkCSPStatus();
            checkInjectionStatus();
            checkPerformanceStatus();
        }
        
        // 检查CSP状态
        function checkCSPStatus() {
            const statusDiv = document.getElementById('cspStatus');
            
            // 尝试执行内联脚本来检测CSP
            try {
                const testScript = document.createElement('script');
                testScript.textContent = 'window.cspTestPassed = true;';
                document.head.appendChild(testScript);
                testScript.remove();
                
                if (window.cspTestPassed) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ CSP允许内联脚本执行';
                } else {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ CSP阻止内联脚本执行';
                }
            } catch (e) {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ CSP严格限制，错误: ' + e.message;
            }
        }
        
        // 检查注入状态
        function checkInjectionStatus() {
            const statusDiv = document.getElementById('injectionStatus');
            
            // 检查是否有网络注入器脚本
            if (typeof chrome !== 'undefined' && chrome.runtime) {
                try {
                    const scriptUrl = chrome.runtime.getURL('content-scripts/network-injector.js');
                    statusDiv.className = 'status success';
                    statusDiv.textContent = '✅ 扩展脚本资源可用: ' + scriptUrl;
                } catch (e) {
                    statusDiv.className = 'status error';
                    statusDiv.textContent = '❌ 扩展脚本资源不可用: ' + e.message;
                }
            } else {
                statusDiv.className = 'status warning';
                statusDiv.textContent = '⚠️ Chrome扩展API不可用';
            }
        }
        
        // 检查Performance API状态
        function checkPerformanceStatus() {
            const statusDiv = document.getElementById('performanceStatus');
            
            if (window.performance && window.performance.getEntriesByType) {
                const entries = window.performance.getEntriesByType('resource');
                const targetEntries = entries.filter(entry => 
                    entry.name && entry.name.includes('querySubOrderList')
                );
                
                if (targetEntries.length > 0) {
                    statusDiv.className = 'status success';
                    statusDiv.textContent = `✅ Performance API检测到 ${targetEntries.length} 个目标接口请求`;
                } else {
                    statusDiv.className = 'status info';
                    statusDiv.textContent = '📊 Performance API可用，但未检测到目标接口';
                }
            } else {
                statusDiv.className = 'status error';
                statusDiv.textContent = '❌ Performance API不可用';
            }
        }
        
        // 测试外部脚本
        function testExternalScript() {
            const resultDiv = document.getElementById('externalScriptResult');
            
            try {
                const script = document.createElement('script');
                script.src = chrome.runtime.getURL('content-scripts/network-injector.js');
                script.onload = () => {
                    resultDiv.innerHTML = '<div class="status success">✅ 外部脚本加载成功</div>';
                    logMessage('✅ 外部脚本加载成功');
                };
                script.onerror = () => {
                    resultDiv.innerHTML = '<div class="status error">❌ 外部脚本加载失败</div>';
                    logMessage('❌ 外部脚本加载失败');
                };
                document.head.appendChild(script);
            } catch (e) {
                resultDiv.innerHTML = '<div class="status error">❌ 外部脚本测试失败: ' + e.message + '</div>';
                logMessage('❌ 外部脚本测试失败: ' + e.message);
            }
        }
        
        // 测试Performance API
        function testPerformanceAPI() {
            const resultDiv = document.getElementById('performanceResult');
            
            if (window.performance && window.performance.getEntriesByType) {
                const entries = window.performance.getEntriesByType('resource');
                const targetEntries = entries.filter(entry => 
                    entry.name && entry.name.includes('querySubOrderList')
                );
                
                let html = '<div class="status info">Performance API检测结果:</div>';
                html += '<div class="code-block">';
                html += `总请求数: ${entries.length}<br>`;
                html += `目标接口数: ${targetEntries.length}<br>`;
                
                targetEntries.forEach((entry, index) => {
                    html += `<br>接口 ${index + 1}: ${entry.name}<br>`;
                    html += `开始时间: ${entry.startTime.toFixed(2)}ms<br>`;
                    html += `持续时间: ${entry.duration.toFixed(2)}ms<br>`;
                });
                
                html += '</div>';
                resultDiv.innerHTML = html;
                
                logMessage(`📊 Performance API检测到 ${targetEntries.length} 个目标接口`);
            } else {
                resultDiv.innerHTML = '<div class="status error">❌ Performance API不可用</div>';
            }
        }
        
        // 测试DOM提取
        function testDOMExtraction() {
            const resultDiv = document.getElementById('domResult');
            
            // 查找可能包含数据的元素
            const scripts = document.querySelectorAll('script');
            let foundData = false;
            
            for (let script of scripts) {
                if (script.textContent && script.textContent.includes('querySubOrderList')) {
                    foundData = true;
                    resultDiv.innerHTML = '<div class="status success">✅ 在script标签中找到相关数据</div>';
                    logMessage('✅ DOM提取: 在script标签中找到相关数据');
                    break;
                }
            }
            
            if (!foundData) {
                resultDiv.innerHTML = '<div class="status warning">⚠️ 未在DOM中找到相关数据</div>';
                logMessage('⚠️ DOM提取: 未找到相关数据');
            }
        }
        
        // 显示手动方法
        function showManualInstructions() {
            const resultDiv = document.getElementById('manualInstructions');
            
            const instructions = `
                <div class="status info">📋 手动复制方法:</div>
                <div class="code-block">
                    1. 打开浏览器开发者工具 (F12)<br>
                    2. 切换到 Network/网络 标签页<br>
                    3. 执行包含 querySubOrderList 的操作<br>
                    4. 在网络面板中找到相关请求<br>
                    5. 右键点击请求 → Copy → Copy Response<br>
                    6. 响应数据已复制到剪切板
                </div>
            `;
            
            resultDiv.innerHTML = instructions;
            logMessage('📋 显示了手动复制方法');
        }
        
        // 日志功能
        function logMessage(message) {
            const log = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            log.innerHTML += `<div>[${timestamp}] ${message}</div>`;
            log.scrollTop = log.scrollHeight;
        }
        
        function clearLog() {
            document.getElementById('testLog').innerHTML = '日志已清空...';
        }
        
        // 页面加载完成后的初始化
        window.addEventListener('load', function() {
            setTimeout(() => {
                logMessage('🚀 CSP修复测试页面加载完成');
                checkAllStatus();
                
                // 检查悬浮球
                setTimeout(() => {
                    const listenerBall = document.getElementById('tm-listener-ball');
                    if (listenerBall) {
                        logMessage('✅ 监听悬浮球已加载');
                    } else {
                        logMessage('❌ 监听悬浮球未找到');
                    }
                }, 2000);
            }, 1000);
        });
    </script>
</body>
</html>
